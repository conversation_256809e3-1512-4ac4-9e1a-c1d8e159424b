import { layers, namedFlavor } from '@protomaps/basemaps';

const TILES_ROOT = 'https://assets.busrouter.sg/tiles/';
const sgTilesPath = TILES_ROOT + 'singapore.pmtiles';
const sgBuildingsTilesPath = TILES_ROOT + 'singapore-buildings.pmtiles';
const sgRailTilesPath = TILES_ROOT + 'sg-rail.geojson';

const GLYPHS_URL =
  'https://protomaps.github.io/basemaps-assets/fonts/{fontstack}/{range}.pbf';
const SPRITE_URL =
  'https://protomaps.github.io/basemaps-assets/sprites/v4/light';

const LIGHT_COLORS = {
  lightBlue: '#bee8f9',
  beige: '#f8f8f3',
  honeydew: '#deecd5',
  white: '#fff',
  whitesmoke: '#f0f0ee',
  mistyrose: '#fcf3f2',
  lavender: '#e9eaf2',
  lightBrown: '#d5d1ca',
  lightYellow: '#e5e5d2',
  linen: '#fbf0ea',
};

const DARK_COLORS = {
  darkBlue: '#112230',
  darkGray: '#08101d',
  darkGreen: '#1a2e1a',
  darkWhite: '#2d2d2d',
  darkSmoke: '#1f1f1f',
  darkRose: '#2a1a1a',
  darkLavender: '#1a1a2a',
  darkBrown: '#2a2520',
  darkYellow: '#2a2a1a',
  darkLinen: '#2a251f',
};

function createFlavor(theme = 'light') {
  const isLight = theme === 'light';
  const colors = isLight ? LIGHT_COLORS : DARK_COLORS;
  const baseFlavor = namedFlavor(isLight ? 'light' : 'black');

  return {
    ...baseFlavor,
    background: isLight ? colors.lightBlue : colors.darkBlue,
    water: isLight ? colors.lightBlue : colors.darkBlue,
    earth: isLight ? colors.beige : colors.darkGray,
    landcover: {
      ...baseFlavor.landcover,
      farmland: isLight ? colors.honeydew : colors.darkGreen,
      forest: isLight ? colors.honeydew : colors.darkGreen,
      grassland: isLight ? colors.honeydew : colors.darkGreen,
    },
    buildings: isLight ? colors.whitesmoke : colors.darkSmoke,
    hospital: isLight ? colors.mistyrose : colors.darkRose,
    park_a: isLight ? colors.honeydew : colors.darkGreen,
    park_b: isLight ? colors.honeydew : colors.darkGreen,
    wood_a: isLight ? colors.honeydew : colors.darkGreen,
    wood_b: isLight ? colors.honeydew : colors.darkGreen,
    scrub_a: isLight ? colors.honeydew : colors.darkGreen,
    scrub_b: isLight ? colors.honeydew : colors.darkGreen,
    aerodrome: isLight ? colors.lavender : colors.darkLavender,
    industrial: isLight ? colors.beige : colors.darkGray,
    military: isLight ? colors.beige : colors.darkGray,
    zoo: isLight ? colors.honeydew : colors.darkGreen,
    minor_a: isLight ? colors.white : colors.darkWhite,
    minor_service: isLight ? colors.white : colors.darkWhite,
    school: isLight ? colors.linen : colors.darkLinen,
    pedestrian: isLight ? colors.beige : colors.darkGray,

    // walk paths
    other: isLight ? colors.lightBrown : colors.darkBrown,
    tunnel_other: isLight ? colors.lightBrown : colors.darkBrown,
    bridges_other: isLight ? colors.lightBrown : colors.darkBrown,

    tunnel_minor: isLight ? colors.lightYellow : colors.darkYellow,
    tunnel_link: isLight ? colors.lightYellow : colors.darkYellow,
    tunnel_major: isLight ? colors.lightYellow : colors.darkYellow,
    tunnel_highway: isLight ? colors.lightYellow : colors.darkYellow,

    bridges_major: isLight ? colors.white : colors.darkWhite,

    subplace_label_halo: isLight ? colors.white : colors.darkWhite,
    city_label_halo: isLight ? colors.white : colors.darkWhite,
    state_label_halo: isLight ? colors.white : colors.darkWhite,
  };
}

export function createMapStyle({ lang = 'en', theme = 'light' } = {}) {
  const flavor = createFlavor(theme);
  const mapLayers = layers('protomaps', flavor, {
    lang,
  });

  const customMapLayers = [];
  let poisLayer = null;

  for (const layer of mapLayers) {
    // Remove these layers
    if (['address_label', 'roads_rail'].includes(layer.id)) {
      continue;
    }

    // Modify layers based on their ID or pattern
    switch (layer.id) {
      // Replace buildings with Overture buildings
      case 'buildings':
        layer.filter = ['!=', ['get', 'is_underground'], true];
        layer.source = 'buildings';
        layer['source-layer'] = 'building';
        layer.paint['fill-outline-color'] = '#d7d7c7';
        layer.paint['fill-opacity'] = 1;
        layer.minzoom = 15;
        break;

      case 'places_subplace':
        layer.layout['text-font'] = ['Noto Sans Medium'];
        layer.paint['text-halo-width'] = 2;
        layer.minzoom = 10;
        break;

      case 'places_locality':
        layer.minzoom = 14;
        break;

      case 'pois':
        poisLayer = layer;
        layer.minzoom = 16;
        // Re-adjust the kind filters in pois
        const poisFilterKindsLiteral = layer.filter
          ?.find?.((v) => v[0] === 'in')
          ?.find((v) => v[0] === 'literal');
        const poisFilterKinds = poisFilterKindsLiteral?.[1];
        console.log('KINDS', layer.filter, poisFilterKinds);
        if (poisFilterKinds?.length) {
          poisFilterKindsLiteral[1] = [
            'aerodrome',
            'animal',
            // 'arrow',
            'beach',
            // 'bench',
            // 'bus_stop',
            'capital',
            // 'drinking_water',
            'ferry_terminal',
            'forest',
            'garden',
            'library',
            'marina',
            'park',
            'peak',
            'school',
            'stadium',
            // 'toilets',
            'townspot',
            // 'train_station',
            'university',
            'zoo',
          ];
        }
        break;
    }

    if (/(water|road).+label/i.test(layer.id)) {
      layer.minzoom = 16;
    }

    // Uppercase road labels
    if (/road.+label/i.test(layer.id)) {
      layer.layout['text-transform'] = 'uppercase';
    }

    // Make roads wider on higher zoom levels
    const ROAD_WIDTH_MAX = 20;
    const ROAD_LARGER_MULTIPLIER = 2;
    const ROAD_LARGEST_MULTIPLIER = 6;
    if (layer['source-layer'] === 'roads' && layer.type === 'line') {
      const isCasing = /casing/i.test(layer.id);
      const lineWidth = isCasing
        ? layer.paint['line-gap-width']
        : layer.paint['line-width'];
      const hasInterpolate = lineWidth?.[0] === 'interpolate';
      if (hasInterpolate) {
        const lastZoom = lineWidth[lineWidth.length - 2];
        const lastWidth = lineWidth[lineWidth.length - 1];
        const isMajor = /(major|highway)/i.test(layer.id);
        const largerWidth =
          lastWidth *
          (isMajor ? ROAD_LARGEST_MULTIPLIER : ROAD_LARGER_MULTIPLIER);
        if (lastZoom < ROAD_WIDTH_MAX) {
          lineWidth.push(ROAD_WIDTH_MAX, largerWidth);
        }
      }
    }

    // Add the layer to our custom array
    customMapLayers.push(layer);

    // Add buildings_label after pois
    if (layer.id === 'pois' && poisLayer) {
      customMapLayers.push({
        id: 'buildings_label',
        type: 'symbol',
        source: 'buildings',
        'source-layer': 'building',
        minzoom: 15,
        layout: {
          'text-field': ['get', '@name'],
          'text-font': ['Noto Sans Regular'],
          'text-max-width': 8,
          'text-size': poisLayer.layout['text-size'],
          'text-padding': 8,
        },
        paint: {
          ...poisLayer.paint,
          'text-color': flavor.pois.slategray,
        },
      });
    }
  }

  console.log(
    'LAYERS',
    customMapLayers.map((l) => l.id),
  );

  const mapStyle = {
    version: 8,
    glyphs: GLYPHS_URL,
    sprite: SPRITE_URL,
    sources: {
      protomaps: {
        type: 'vector',
        url: `pmtiles://${sgTilesPath}`,
        attribution:
          '<a href="https://protomaps.com" target="_blank">Protomaps</a> © <a href="https://openstreetmap.org" target="_blank">OpenStreetMap</a>',
      },
      buildings: {
        type: 'vector',
        url: `pmtiles://${sgBuildingsTilesPath}`,
        // Don't need OSM because already covered by the one above
        attribution:
          '<a href="https://overturemaps.org" target="_blank">Overture Maps Foundation</a>',
      },
      'sg-rail': {
        type: 'geojson',
        data: sgRailTilesPath,
        attribution:
          '© <a href="https://www.smrt.com.sg/" target="_blank" title="Singapore Mass Rapid Transit">SMRT</a> © <a href="https://www.sbstransit.com.sg/" target="_blank" title="Singapore Bus Services">SBS</a>',
      },
    },
    layers: customMapLayers,
  };

  return mapStyle;
}

export { sgRailTilesPath };
