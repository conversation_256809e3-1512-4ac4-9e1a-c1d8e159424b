/* Shared CSS Variables for theming across arrival pages */

/* Light theme colors */
:root {
  /* Background colors */
  --bg-primary: #f7f7f7;
  --bg-secondary: #fff;
  --bg-overlay: rgba(240, 240, 240, 0.75);
  --bg-overlay-light: rgba(255, 255, 255, 0.5);
  --bg-overlay-lighter: rgba(255, 255, 255, 0.8);
  --bg-row-alt: #fafafa;
  --bg-stop-tag: #f01b4822;
  --bg-legend: #fff;
  --bg-time-bar: #ddd;
  --bg-time-bar-empty: #eee;
  --bg-time-range: #007aff;
  
  /* Text colors */
  --text-primary: #000;
  --text-secondary: #666;
  --text-tertiary: #999;
  --text-quaternary: #aaa;
  --text-disabled: rgba(0, 0, 0, 0.3);
  --text-stop: #f01b48;
  --text-service: #3a6727;
  --text-accent: #007aff;
  
  /* Border colors */
  --border-light: #eee;
  --border-light-rgba: rgba(0, 0, 0, 0.05);
  --border-medium: #ccc;
  --border-legend: #aaa;
  --border-dashed: rgba(0, 0, 0, 0.5);
  
  /* Shadow colors */
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.3);
  
  /* Semantic color variables */
  --color-surface: #fff;
}

/* Dark theme colors */
[data-theme="dark"] {
  /* Background colors */
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-overlay: rgba(45, 45, 45, 0.75);
  --bg-overlay-light: rgba(45, 45, 45, 0.5);
  --bg-overlay-lighter: rgba(45, 45, 45, 0.8);
  --bg-row-alt: #333;
  --bg-stop-tag: #f01b4833;
  --bg-legend: #2d2d2d;
  --bg-time-bar: #555;
  --bg-time-bar-empty: #444;
  --bg-time-range: #4da6ff;
  
  /* Text colors */
  --text-primary: #fff;
  --text-secondary: #ccc;
  --text-tertiary: #999;
  --text-quaternary: #777;
  --text-disabled: rgba(255, 255, 255, 0.3);
  --text-stop: #ff4d7a;
  --text-service: #7bc95f;
  --text-accent: #4da6ff;
  
  /* Border colors */
  --border-light: #444;
  --border-light-rgba: rgba(255, 255, 255, 0.1);
  --border-medium: #555;
  --border-legend: #666;
  --border-dashed: rgba(255, 255, 255, 0.5);
  
  /* Shadow colors */
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-medium: rgba(0, 0, 0, 0.5);
  
  /* Semantic color variables */
  --color-surface: #1a1a1a;
}
