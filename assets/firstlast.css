/* Import shared theme variables */
@import './theme-variables.css';

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding: 0;
  margin: 0;
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

main {
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  padding: 0;
  margin: 1em 10px;
  font-size: 1em;
}
h1 b {
  font-size: 1.2em;
}

.stop-tag {
  display: inline-block;
  padding: 2px 5px;
  border-radius: 4px;
  background-color: var(--bg-stop-tag);
  color: var(--text-stop);
  flex-shrink: 0;
  font-variant-numeric: tabular-nums;
  font-weight: 500;
  margin: 2px 0;
  font-size: 0.8em;
}

.legend {
  margin: 10px;
  padding: 0;
  font-size: 0.8em;
  color: var(--text-secondary);
}
.legend span {
  display: inline-block;
}
.legend > span {
  margin-right: 1.5em;
  margin-bottom: 0.5em;
}
.legend .abbr {
  padding: 4px 7px;
  font-size: 0.9em;
  color: var(--text-tertiary);
  border-radius: 3px;
  border: 1px solid var(--border-legend);
  background-color: var(--bg-legend);
}

table {
  width: 100%;
  border: 0;
  border-spacing: 0;
  clear: both;
}

table td,
table th {
  padding: 5px 8px;
}

table td[title='-'] {
  color: var(--text-disabled);
  pointer-events: none;
  user-select: none;
}

table thead tr > * {
  white-space: nowrap;
  position: sticky;
  top: 0;
  background-color: var(--bg-overlay);
  border-bottom: 1px solid var(--border-medium);
  z-index: 100;
}
table thead th {
  font-weight: normal;
  color: var(--text-tertiary);
  text-transform: uppercase;
  font-size: 0.8em;
}
table thead th:first-child {
  text-align: left;
}

table td[rowspan] {
  font-size: 1.8em;
}

table tbody {
  background-color: var(--bg-secondary);
}
table tbody > tr:first-child > td[rowspan],
table tbody > tr:last-child > * {
  border-bottom: 1px solid var(--border-medium);
}
table tbody th abbr {
  font-weight: normal;
  color: var(--text-tertiary);
  text-align: right;
  font-size: 0.8em;
}
table tbody th ~ td {
  text-align: center;
  font-feature-settings: 'tnum';
  font-variant-numeric: tabular-nums;
}
table tbody tr:nth-child(2) > * {
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
}

.insignificant {
  opacity: 0.5;
}

abbr {
  text-decoration: none;
}

.timerange-header,
.time-cell,
.timerange-note {
  display: none;
}
.timerange-header > span {
  display: inline-block;
  width: 25%;
  text-align: left;
}
.timerange-indicator {
  top: 0;
  position: absolute;
  /* background-color: #007aff; */
  background-image: linear-gradient(
    transparent 2.5em,
    var(--text-accent) 2.5em,
    var(--text-accent) 60%,
    transparent 90%
  );
  width: 2px;
  height: 100vh;
  pointer-events: none;
  font-feature-settings: 'tnum';
  font-variant-numeric: tabular-nums;
}
.timerange-indicator > span {
  position: absolute;
  top: 2em;
  transform: translateX(-50%);
  padding: 3px 6px;
  border-radius: 123123px;
  background-color: var(--text-accent);
  color: var(--color-surface);
  line-height: 1;
  font-size: smaller;
}
.time-cell {
  background: linear-gradient(
    to right,
    transparent,
    transparent 24.8%,
    var(--border-light) 24.9%,
    var(--border-light) 25.1%,
    transparent 25.2%,
    transparent 49.8%,
    var(--bg-time-bar) 49.9%,
    var(--bg-time-bar) 50.1%,
    transparent 50.2%,
    transparent 74.8%,
    var(--border-light) 74.9%,
    var(--border-light) 75.1%,
    transparent 75.2%,
    transparent
  );
}
.time-ranger {
  position: relative;
  width: 100%;
  min-width: 240px;
  background-color: var(--bg-time-bar);
  height: 5px;
  border-radius: 2020px;
  overflow: hidden;
}
.time-ranger.nada {
  background-color: var(--bg-time-bar-empty);
}
.time-ranger .bar {
  position: absolute;
  border-radius: 2020px;
  height: 5px;
  opacity: 0.5;
  min-width: 5px;
  background-color: var(--bg-time-range);
}
.time-duration {
  line-height: 1;
  display: block;
  font-size: 10px;
  color: var(--text-accent);
  width: 100%;
  text-align: right;
  z-index: 1;
  position: relative;
  margin-bottom: -10px;
}

@media (min-width: 640px) {
  .timerange-header,
  .time-cell {
    display: table-cell;
  }
  .timerange-note {
    display: block;
  }

  .ads {
    float: right;
    width: 320px;
    margin-top: -1em;
  }
}

tfoot td {
  font-size: 0.9em;
  padding: 10px 8px;
  color: var(--text-tertiary);
}
tfoot p {
  margin-top: 0;
  padding-top: 0;
}
tfoot a {
  color: inherit;
}

.placeholder {
  color: var(--text-quaternary);
  letter-spacing: -1px;
  word-spacing: 1ex;
  animation: glowing infinite alternate 1s ease-in-out both;
  pointer-events: none;
  user-select: none;
}
@keyframes glowing {
  0% {
    opacity: 0.2;
  }
  100% {
    opacity: 0.8;
  }
}

blink {
  animation: blink 0.5s linear infinite alternate;
}
@keyframes blink {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
