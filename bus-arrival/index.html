<!DOCTYPE html>
<html lang="en">
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bus arrival times</title>
  <link rel="stylesheet" href="../assets/arrival.css" />
  <link rel="preconnect" href="https://arrivelah2.busrouter.sg" crossorigin />
  <link rel="dns-prefetch" href="https://arrivelah2.busrouter.sg" crossorigin />
  <link rel="preload" as="image" href="../assets/images/stop-active.svg" />
  <link rel="preload" as="image" href="../assets/images/bus-single.svg" />
  <link rel="preload" as="image" href="../assets/images/bus-double.svg" />
  <link rel="apple-touch-icon" href="../icons/icon-192.png" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-title" content="Bus arrival times" />
  <main>
    <div id="arrivals"></div>
  </main>

  <!-- Theme selector -->
  <div id="theme-selector" style="position: fixed; top: 10px; right: 10px; z-index: 1000; background: var(--bg-overlay); border-radius: 8px; padding: 8px; backdrop-filter: blur(10px);">
    <select id="theme-select" style="background: var(--bg-secondary); color: var(--text-primary); border: 1px solid var(--border-light); border-radius: 4px; padding: 4px 8px; font-size: 12px;">
      <option value="auto">Auto</option>
      <option value="light">Light</option>
      <option value="dark">Dark</option>
    </select>
  </div>
  <script
    async
    src="https://www.googletagmanager.com/gtag/js?id=G-D34FLYKNNR"
  ></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());
    window._GA_TRACKING_ID = 'G-D34FLYKNNR';
    gtag('config', window._GA_TRACKING_ID, { send_page_view: false });
    window.gtag = gtag;
  </script>
  <script src="../assets/arrival.js" type="module"></script>

  <!-- Theme switching script -->
  <script>
    // Theme management
    const themeSelect = document.getElementById('theme-select');
    const html = document.documentElement;

    // Load saved theme or default to auto
    const savedTheme = localStorage.getItem('theme') || 'auto';
    themeSelect.value = savedTheme;

    function applyTheme(theme) {
      if (theme === 'auto') {
        // Use system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        html.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
      } else {
        html.setAttribute('data-theme', theme);
      }
    }

    // Apply initial theme
    applyTheme(savedTheme);

    // Listen for theme changes
    themeSelect.addEventListener('change', (e) => {
      const theme = e.target.value;
      localStorage.setItem('theme', theme);
      applyTheme(theme);
    });

    // Listen for system theme changes when auto is selected
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
      if (themeSelect.value === 'auto') {
        applyTheme('auto');
      }
    });
  </script>
  <script type="text/javascript">
    (function (window, document, dataLayerName, id) {
      (window[dataLayerName] = window[dataLayerName] || []),
        window[dataLayerName].push({
          start: new Date().getTime(),
          event: 'stg.start',
        });
      var scripts = document.getElementsByTagName('script')[0],
        tags = document.createElement('script');
      function stgCreateCookie(a, b, c) {
        var d = '';
        if (c) {
          var e = new Date();
          e.setTime(e.getTime() + 24 * c * 60 * 60 * 1e3),
            (d = '; expires=' + e.toUTCString());
          f = '; SameSite=Strict';
        }
        document.cookie = a + '=' + b + d + f + '; path=/';
      }
      var isStgDebug =
        (window.location.href.match('stg_debug') ||
          document.cookie.match('stg_debug')) &&
        !window.location.href.match('stg_disable_debug');
      stgCreateCookie('stg_debug', isStgDebug ? 1 : '', isStgDebug ? 14 : -1);
      var qP = [];
      dataLayerName !== 'dataLayer' &&
        qP.push('data_layer_name=' + dataLayerName),
        isStgDebug && qP.push('stg_debug');
      var qPString = qP.length > 0 ? '?' + qP.join('&') : '';
      (tags.async = !0),
        (tags.src = 'https://ca.containers.piwik.pro/' + id + '.js' + qPString),
        scripts.parentNode.insertBefore(tags, scripts);
      !(function (a, n, i) {
        a[n] = a[n] || {};
        for (var c = 0; c < i.length; c++)
          !(function (i) {
            (a[n][i] = a[n][i] || {}),
              (a[n][i].api =
                a[n][i].api ||
                function () {
                  var a = [].slice.call(arguments, 0);
                  'string' == typeof a[0] &&
                    window[dataLayerName].push({
                      event: n + '.' + i + ':' + a[0],
                      parameters: [].slice.call(arguments, 1),
                    });
                });
          })(i[c]);
      })(window, 'ppms', ['tm', 'cm']);
    })(window, document, 'dataLayer', 'f84f7808-9fe5-444d-884d-3f61a5d8b01e');
  </script>
</html>
