<!DOCTYPE html>
<html lang="en">
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="color-scheme" content="light dark" />
  <title>Bus arrival times</title>
  <link rel="stylesheet" href="../assets/arrival.css" />
  <link rel="preconnect" href="https://arrivelah2.busrouter.sg" crossorigin />
  <link rel="dns-prefetch" href="https://arrivelah2.busrouter.sg" crossorigin />
  <link rel="preload" as="image" href="../assets/images/stop-active.svg" />
  <link rel="preload" as="image" href="../assets/images/bus-single.svg" />
  <link rel="preload" as="image" href="../assets/images/bus-double.svg" />
  <link rel="apple-touch-icon" href="../icons/icon-192.png" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-title" content="Bus arrival times" />
  <main>
    <div id="arrivals"></div>
  </main>
  <script
    async
    src="https://www.googletagmanager.com/gtag/js?id=G-D34FLYKNNR"
  ></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());
    window._GA_TRACKING_ID = 'G-D34FLYKNNR';
    gtag('config', window._GA_TRACKING_ID, { send_page_view: false });
    window.gtag = gtag;
  </script>
  <script src="../assets/arrival.js" type="module"></script>
  <script type="text/javascript">
    (function (window, document, dataLayerName, id) {
      (window[dataLayerName] = window[dataLayerName] || []),
        window[dataLayerName].push({
          start: new Date().getTime(),
          event: 'stg.start',
        });
      var scripts = document.getElementsByTagName('script')[0],
        tags = document.createElement('script');
      function stgCreateCookie(a, b, c) {
        var d = '';
        if (c) {
          var e = new Date();
          e.setTime(e.getTime() + 24 * c * 60 * 60 * 1e3),
            (d = '; expires=' + e.toUTCString());
          f = '; SameSite=Strict';
        }
        document.cookie = a + '=' + b + d + f + '; path=/';
      }
      var isStgDebug =
        (window.location.href.match('stg_debug') ||
          document.cookie.match('stg_debug')) &&
        !window.location.href.match('stg_disable_debug');
      stgCreateCookie('stg_debug', isStgDebug ? 1 : '', isStgDebug ? 14 : -1);
      var qP = [];
      dataLayerName !== 'dataLayer' &&
        qP.push('data_layer_name=' + dataLayerName),
        isStgDebug && qP.push('stg_debug');
      var qPString = qP.length > 0 ? '?' + qP.join('&') : '';
      (tags.async = !0),
        (tags.src = 'https://ca.containers.piwik.pro/' + id + '.js' + qPString),
        scripts.parentNode.insertBefore(tags, scripts);
      !(function (a, n, i) {
        a[n] = a[n] || {};
        for (var c = 0; c < i.length; c++)
          !(function (i) {
            (a[n][i] = a[n][i] || {}),
              (a[n][i].api =
                a[n][i].api ||
                function () {
                  var a = [].slice.call(arguments, 0);
                  'string' == typeof a[0] &&
                    window[dataLayerName].push({
                      event: n + '.' + i + ':' + a[0],
                      parameters: [].slice.call(arguments, 1),
                    });
                });
          })(i[c]);
      })(window, 'ppms', ['tm', 'cm']);
    })(window, document, 'dataLayer', 'f84f7808-9fe5-444d-884d-3f61a5d8b01e');
  </script>
</html>
